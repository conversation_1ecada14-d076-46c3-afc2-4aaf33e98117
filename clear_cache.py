#!/usr/bin/env python3
"""
Simple script to clear the cache directory.
Use this when you want to force fresh API calls.
"""

import os
import shutil

def clear_cache():
    """Clear the cache directory."""
    cache_dir = os.path.join(os.path.dirname(__file__), "cache")
    if os.path.exists(cache_dir):
        shutil.rmtree(cache_dir)
        print(f"✓ Cleared cache directory: {cache_dir}")
        print("All cached data has been removed. Next API calls will fetch fresh data.")
    else:
        print("ℹ No cache directory found - nothing to clear")

if __name__ == "__main__":
    clear_cache()
