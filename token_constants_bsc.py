"""
Constants related to BSC (Binance Smart Chain) tokens.
"""

# BSC Token contract addresses (0x format)
BSC_USDC = "******************************************"  # USD Coin
BSC_USDT = "******************************************"  # Tether USD
BSC_BUSD = "******************************************"  # Binance USD (deprecated)
BSC_DAI = "******************************************"   # Dai Stablecoin

# Native BNB and Wrapped BNB
BNB_NATIVE = "BNB"  # Native BNB (used in swap data)
WBNB_TOKEN = "******************************************"  # Wrapped BNB

# List of tokens to consider as BNB (native and wrapped)
BNB_TOKENS = [BNB_NATIVE, WBNB_TOKEN]

# List of tokens to consider as stable coins
STABLE_TOKENS = [BSC_USDC, BSC_USDT, BSC_BUSD, BSC_DAI]

# List of tokens to exclude from analysis (stablecoins and BNB)
EXCLUDED_TOKENS = STABLE_TOKENS + BNB_TOKENS

# Common BSC DEX router addresses for reference
PANCAKESWAP_V2_ROUTER = "******************************************"
PANCAKESWAP_V3_ROUTER = "******************************************"
PANCAKESWAP_V1_ROUTER = "******************************************"

# BSC Chain ID
BSC_CHAIN_ID = "bsc"
