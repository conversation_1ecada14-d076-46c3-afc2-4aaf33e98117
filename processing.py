from clients.helius_client import Helius<PERSON><PERSON>
from clients.coingecko_client import CoinGeckoClient
from datetime import datetime
import logging
from token_constants import SOL_MINT, SOL_TOKENS, STABLE_TOKENS
import pandas as pd

def munge_transactions(transactions, helius_client, coingecko_client):
    """
    Extract token information from SWAP transactions.
    
    Args:
        transactions (list): List of transaction objects
        helius_client (HeliusClient): Helius client instance
        coingecko_client (CoinGeckoClient): CoinGecko client instance
        
    Returns:
        list: List of dictionaries with token swap details
    """
    token_swaps = []
    
    # Token mint addresses and lists are now imported from token_constants module
    
    # First, process all transactions and create entries with empty input_id and output_id
    for tx in transactions:
        try:
            # Check if it's a SWAP transaction
            if tx.get("type") != "SWAP":
                continue
            
            timestamp = tx.get("timestamp")
            signature = tx.get("signature")
            
            # Look for swap event
            if "events" in tx and "swap" in tx["events"]:
                swap = tx["events"]["swap"]
                
                # Get the first input and output tokens
                token_inputs = swap.get("tokenInputs", [])
                token_outputs = swap.get("tokenOutputs", [])
                
                # Check for native SOL transactions
                native_input = swap.get("nativeInput")
                native_output = swap.get("nativeOutput")
                
                if not token_inputs and not native_input:
                    logging.info(swap);
                    exit()
                    
                # Handle native SOL input
                if native_input:
                    input_amount = float(native_input.get("amount", 0)) / 10**9  # Convert lamports to SOL
                    input_mint = SOL_MINT
                else:
                    # Assume only one token input
                    token_input = token_inputs[0]
                    input_mint = token_input.get("mint")
                    input_decimals = helius_client.get_token_decimals(input_mint)
                    input_amount = float(token_input.get("rawTokenAmount", {}).get("tokenAmount", 0)) / 10**input_decimals
                
                # Handle native SOL output
                if native_output:
                    output_amount = float(native_output.get("amount", 0)) / 10**9  # Convert lamports to SOL
                    output_mint = SOL_MINT
                else:
                    token_output = token_outputs[0]
                    output_mint = token_output.get("mint")
                    output_decimals = helius_client.get_token_decimals(output_mint)
                    output_amount = float(token_output.get("rawTokenAmount", {}).get("tokenAmount", 0)) / 10**output_decimals
                
                # Determine action based on token types
                # Action is "buy" if input is a stablecoin or SOL
                if input_mint in STABLE_TOKENS or native_input:
                    action = "buy"
                    price = input_amount / output_amount
                elif output_mint in STABLE_TOKENS or native_output:
                    action = "sell"
                    price = output_amount / input_amount
                else:
                    logging.info(f"Neither input or output is not stable token or SOL: {signature}")
                
                # Adjust price if either token is SOL (native or wrapped)
                if native_input or native_output or input_mint in SOL_TOKENS or output_mint in SOL_TOKENS:
                    logging.debug("Getting SOL price")
                    date_str = datetime.fromtimestamp(timestamp).strftime('%d-%m-%Y')
                    sol_price = coingecko_client.get_solana_price(date_str)
                    if sol_price is not None:
                        if native_input or input_mint in SOL_TOKENS:
                            # If input is SOL, multiply by SOL price to get USD value
                            price = price * sol_price
                        elif native_output or output_mint in SOL_TOKENS:
                            # If output is SOL, multiply by SOL price to get USD value
                            price = price * sol_price
                
                # Add entry with empty input_id and output_id
                token_swaps.append({
                    "timestamp": timestamp,
                    "date": datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S'),
                    "signature": signature,
                    "user_account": token_inputs[0].get("userAccount") if token_inputs else native_input.get("account"),
                    "input_mint": input_mint,
                    "output_mint": output_mint,
                    "input_id": "",  # Will be filled later
                    "output_id": "",  # Will be filled later
                    "input_amount": input_amount,
                    "output_amount": output_amount,
                    "action": action,
                    "price": price,
                    "is_sol_trade": native_input is not None or native_output is not None or input_mint in SOL_TOKENS or output_mint in SOL_TOKENS
                })
                
        except Exception as e:
            logging.error(f"Error processing transaction {tx.get('signature')}: {str(e)}")
    
    # Now extract unique token mints
    unique_mints = set()
    for swap in token_swaps:
        unique_mints.add(swap["input_mint"])
        unique_mints.add(swap["output_mint"])
    
    # Create token ID mapping
    token_id_mapping = coingecko_client.create_token_id_mapping(unique_mints)
    
    # Update input_id and output_id fields with token IDs from mapping
    for swap in token_swaps:
        swap["input_id"] = token_id_mapping.get(swap["input_mint"])
        swap["output_id"] = token_id_mapping.get(swap["output_mint"])
            
    return token_swaps, token_id_mapping

def munge_swaps_for_token(token_swaps, token_mint, token_id):
    """
    Filter and process swaps for a specific token, converting to DataFrame.
    
    Args:
        token_swaps (list): List of dictionaries containing token swap data
        token_mint (str): Token mint address to filter by
        token_id (str): Token ID
        
    Returns:
        DataFrame: Processed swaps with additional fields
    """
    # Filter swaps where this token is either input or output
    filtered_swaps = [
        swap for swap in token_swaps
        if swap["input_mint"] == token_mint or swap["output_mint"] == token_mint
    ]
    
    processed_swaps = []
    for swap in filtered_swaps:
        # Keep only the amount associated with this token
        amount = swap["input_amount"] if swap["input_mint"] == token_mint else swap["output_amount"]
        
        # Create a new dictionary with the required fields
        processed_swap = {
            "date": swap["date"],
            "input_mint": swap["input_mint"],
            "output_mint": swap["output_mint"],
            "input_id": swap["input_id"],
            "output_id": swap["output_id"],
            "amount": amount,
            "action": swap["action"],
            "price": swap["price"],
            "is_sol_trade": swap["is_sol_trade"]
        }
        processed_swaps.append(processed_swap)
    
    # Convert to DataFrame
    df = pd.DataFrame(processed_swaps)
    
    # Add column indicating which token this DataFrame is associated with
    df["associated_token"] = token_mint
    
    # Add column indicating the ID of the associated token
    df["associated_token_id"] = token_id
    
    # Sort by date
    df = df.sort_values("date")
    
    return df

def munge_swaps(token_swaps, excluded_tokens):
    """
    Process swaps, extracting the amount not associated with excluded tokens.
    
    Args:
        token_swaps (list): List of dictionaries containing token swap data
        excluded_tokens (set): Set of token mint addresses to exclude when determining amount
        
    Returns:
        DataFrame: Processed swaps with amount reflecting non-excluded token
    """
    munged_swaps = []
    for swap in token_swaps:
        amount = None
        
        # Determine the amount based on non-excluded tokens
        if swap["input_mint"] not in excluded_tokens:
            amount = swap["input_amount"]
        elif swap["output_mint"] not in excluded_tokens:
            amount = swap["output_amount"]
        else:
            logging.warning(f"Both input and output tokens are excluded for swap {swap['signature']}")
            continue # Skip this swap if both tokens are excluded
            
        # Create a new dictionary with the required fields
        munged_swap = {
            "date": swap["date"],
            "input_mint": swap["input_mint"],
            "output_mint": swap["output_mint"],
            "input_id": swap["input_id"],
            "output_id": swap["output_id"],
            "amount": amount,
            "action": swap["action"],
            "price": swap["price"],
            "is_sol_trade": swap["is_sol_trade"]
        }
        munged_swaps.append(munged_swap)
        
    # Convert to DataFrame
    df = pd.DataFrame(munged_swaps)
    
    # Sort by date
    if not df.empty:
        df = df.sort_values("date")
        
    return df
