#!/usr/bin/env python3
"""
Test script for the MoralisClient to verify basic functionality.
"""

import os
import logging
import shutil
import argparse
from dotenv import load_dotenv
from clients.morails_client import MoralisClient, BSC_USDT

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)

def clear_cache():
    """Clear the cache directory to force fresh API calls."""
    cache_dir = os.path.join(os.path.dirname(__file__), "cache")
    if os.path.exists(cache_dir):
        shutil.rmtree(cache_dir)
        print(f"✓ Cleared cache directory: {cache_dir}")
    else:
        print("ℹ No cache directory found")

def test_moralis_client(clear_cache_first=False):
    """Test basic MoralisClient functionality."""
    # Clear cache if requested
    if clear_cache_first:
        clear_cache()

    # Load environment variables
    load_dotenv()
    
    # You'll need to add MORALIS_API_KEY to your .env file
    moralis_api_key = os.getenv("MORALIS_API_KEY")
    
    if not moralis_api_key:
        print("Please add MORALIS_API_KEY to your .env file to test the client")
        print("You can get a free API key from https://moralis.io/")
        return
    
    # Initialize the client
    client = MoralisClient(moralis_api_key)
    print(f"✓ MoralisClient initialized successfully")
    
    # Get wallet address from .env file or use default
    test_wallet = os.getenv("WALLET_ADDRESS", "******************************************")

    print(f"\nTesting with wallet: {test_wallet}")
    print(f"(Using wallet from .env WALLET_ADDRESS or default if not set)")
    
    # Test getting BNB price
    try:
        bnb_price = client.get_bnb_price()
        print(f"✓ BNB price: ${bnb_price}")
    except Exception as e:
        print(f"✗ Error getting BNB price: {e}")
    
    # Test getting token metadata
    try:
        usdt_metadata = client.get_token_metadata(BSC_USDT)
        print(f"✓ USDT metadata: {usdt_metadata}")
    except Exception as e:
        print(f"✗ Error getting USDT metadata: {e}")
    
    # Test getting token price
    try:
        usdt_price = client.get_token_price(BSC_USDT)
        print(f"✓ USDT price: ${usdt_price}")
    except Exception as e:
        print(f"✗ Error getting USDT price: {e}")
    
    # Test getting wallet token balances (this might be empty for the test wallet)
    try:
        balances = client.get_wallet_token_balances(test_wallet)
        print(f"✓ Wallet token balances: Found {len(balances)} tokens")
        if balances:
            print(f"  First few tokens: {balances[:3]}")
    except Exception as e:
        print(f"✗ Error getting wallet balances: {e}")
    
    # Test getting swaps (this might take a while and could be empty)
    try:
        print(f"\nFetching swaps for the last 1 month (this may take a while)...")
        swaps = client.get_swaps(test_wallet, months=1)
        print(f"✓ Found {len(swaps)} swaps in the last month")
        if swaps:
            print(f"  First swap: {swaps[0]}")
    except Exception as e:
        print(f"✗ Error getting swaps: {e}")
    
    print(f"\n✓ MoralisClient test completed!")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Test MoralisClient functionality")
    parser.add_argument("--clear-cache", action="store_true",
                       help="Clear cache before running tests to force fresh API calls")
    args = parser.parse_args()

    test_moralis_client(clear_cache_first=args.clear_cache)
