# Moralis Client for BSC (Binance Smart Chain)

This document describes the `MoralisClient` class that provides functionality similar to the `HeliusClient` but for the Binance Smart Chain (BSC) using the Moralis API.

## Overview

The `MoralisClient` is designed to fetch swap transactions, token metadata, and price data from the BSC blockchain using the Moralis Web3 Data API. It follows the same patterns as the existing `HeliusClient` with caching and error handling.

## Setup

### 1. Get a Moralis API Key

1. Visit [https://moralis.io/](https://moralis.io/)
2. Sign up for a free account
3. Create a new project
4. Copy your API key

### 2. Add API Key to Environment

Add your Moralis API key to your `.env` file:

```bash
MORALIS_API_KEY="your_moralis_api_key_here"
```

### 3. Install Dependencies

The client uses the same dependencies as the existing project:
- `requests` - for HTTP API calls
- `python-dotenv` - for environment variable management
- Standard library modules: `datetime`, `logging`, `functools`

## Usage

### Basic Initialization

```python
from clients.morails_client import MoralisClient
import os
from dotenv import load_dotenv

load_dotenv()
moralis_api_key = os.getenv("MORALIS_API_KEY")
client = MoralisClient(moralis_api_key)
```

### Available Methods

#### 1. Get Swap Transactions

```python
# Get swaps for the last 6 months (default)
swaps = client.get_swaps("******************************************")

# Get swaps for the last 3 months
swaps = client.get_swaps("******************************************", months=3)
```

#### 2. Get Transactions (Helius-compatible interface)

```python
# Compatible with HeliusClient interface
transactions = client.get_transactions("******************************************", months=6, transaction_type="SWAP")
```

#### 3. Get Token Metadata

```python
from clients.morails_client import BSC_USDT

# Get token metadata including decimals, symbol, name
metadata = client.get_token_metadata(BSC_USDT)
print(metadata)
# Output: {'decimals': 18, 'symbol': 'USDT', 'name': 'Tether USD', 'address': '0x55d398...'}
```

#### 4. Get Token Decimals

```python
# Get just the decimals for a token
decimals = client.get_token_decimals(BSC_USDT)
print(decimals)  # Output: 18
```

#### 5. Get Token Prices

```python
# Get BNB price
bnb_price = client.get_bnb_price()
print(f"BNB price: ${bnb_price}")

# Get any token price
usdt_price = client.get_token_price(BSC_USDT)
print(f"USDT price: ${usdt_price}")
```

#### 6. Get Wallet Token Balances

```python
# Get current token balances for a wallet
balances = client.get_wallet_token_balances("******************************************")
```

## BSC Constants

The client includes common BSC token addresses:

```python
from clients.morails_client import (
    BSC_NATIVE_TOKEN,  # WBNB
    BSC_USDT,          # Tether USD
    BSC_USDC,          # USD Coin
    BSC_BUSD,          # Binance USD (deprecated)
    PANCAKESWAP_V2_ROUTER,
    PANCAKESWAP_V3_ROUTER,
    PANCAKESWAP_V1_ROUTER
)
```

## Caching

The client uses the same caching strategy as the HeliusClient:

- **File caching**: Results are cached to disk with TTL (Time To Live)
- **LRU caching**: In-memory caching for frequently accessed data
- **Cache TTL**: 
  - Swap/transaction data: 240 hours (10 days)
  - Token metadata: 240 hours (10 days)
  - Price data: 1 hour (prices change frequently)

## Error Handling

The client includes comprehensive error handling:

- API errors are logged with details
- Network timeouts are handled gracefully
- Invalid responses return default values
- Pagination errors are caught and logged

## Testing

Run the test script to verify your setup:

```bash
# Basic test (uses WALLET_ADDRESS from .env or default)
python test_moralis_client.py

# Test with fresh data (clears cache first)
python test_moralis_client.py --clear-cache
```

The test script will:
- Read the wallet address from your `.env` file (`WALLET_ADDRESS`)
- Test all major functionality
- Help you verify that your API key is working correctly

### Important: Cache Behavior

The client uses aggressive caching to minimize API calls. If you change the `WALLET_ADDRESS` in your `.env` file and still see the same results, it's because the previous results are cached. Use one of these solutions:

```bash
# Option 1: Clear cache before testing
python test_moralis_client.py --clear-cache

# Option 2: Use the dedicated cache clearing script
python clear_cache.py

# Option 3: Manually delete the cache directory
rm -rf cache/
```

## Differences from HeliusClient

| Feature | HeliusClient (Solana) | MoralisClient (BSC) |
|---------|----------------------|---------------------|
| Blockchain | Solana | Binance Smart Chain |
| API Provider | Helius | Moralis |
| Address Format | Base58 | 0x hex format |
| Native Token | SOL/WSOL | BNB/WBNB |
| Transaction Types | SWAP, TRANSFER, etc. | SWAP (dedicated endpoint) |
| Decimals Default | 9 | 18 |

## API Endpoints Used

The client uses these Moralis API endpoints:

- `GET /wallets/{address}/swaps` - Get swap transactions
- `POST /erc20/metadata` - Get token metadata
- `GET /erc20/{address}/price` - Get token prices
- `GET /{address}/erc20` - Get wallet token balances

## Rate Limits

Moralis has rate limits that vary by plan:
- Free tier: 40,000 requests per month
- Pro tier: Higher limits available

The caching system helps minimize API calls and stay within rate limits.

## Troubleshooting

### Common Issues

1. **"API key required"**: Make sure `MORALIS_API_KEY` is set in your `.env` file
2. **"Invalid address format"**: BSC addresses must be in 0x format (42 characters)
3. **"Rate limit exceeded"**: Wait or upgrade your Moralis plan
4. **"No swaps found"**: The wallet might not have any swap transactions in the specified time period
5. **"Same results after changing wallet address"**: This is due to caching. Clear the cache using:
   ```bash
   python test_moralis_client.py --clear-cache
   # or
   python clear_cache.py
   ```

### Debug Mode

Enable debug logging to see detailed API calls:

```python
import logging
logging.getLogger().setLevel(logging.DEBUG)
```

## Integration with Existing Code

The `MoralisClient` is designed to be a drop-in replacement for `HeliusClient` when working with BSC data. The main methods (`get_transactions`, `get_token_decimals`) have compatible signatures.
