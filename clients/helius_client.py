from datetime import datetime, timed<PERSON>ta
import re
import requests
import logging
from functools import lru_cache
from utils.utils import file_cache

class HeliusClient:
    """Client for interacting with Helius API to fetch Solana transactions."""
    
    def __init__(self, api_key):
        self.api_key = api_key
        self.base_url = "https://api.helius.xyz/v0"
        
    @file_cache(ttl_hours=240)
    @lru_cache(maxsize=1024)
    def get_transactions(self, wallet_address, months=6, transaction_type="SWAP"):
        """
        Fetch SWAP transactions for a wallet address from the past specified months.
        Uses both file caching and LRU caching to reduce API calls.
        
        Args:
            wallet_address (str): Solana wallet address
            months (int): Number of months to look back
            transaction_type (str): Type of transaction to filter for
            
        Returns:
            list: List of transactions
        """
        logging.info(f"Fetching {transaction_type} transactions for {wallet_address}")
        
        # Calculate timestamp for months ago
        months_ago = datetime.now() - timedelta(days=30 * months)
        before_timestamp = int(months_ago.timestamp())
        
        all_transactions = []
        url = f"{self.base_url}/addresses/{wallet_address}/transactions"
        params = {
            "api-key": self.api_key,
            "type": transaction_type,
            "limit": 100
        }
        
        # Use pagination to get all transactions
        last_signature = None
        while True:
            if last_signature:
                params["before"] = last_signature
                
            response = requests.get(url, params=params)
            
            if response.status_code != 200:
                error_response = response.json()
                if "error" in error_response and "before" in error_response["error"]:
                    # Extract the signature from the error message
                    signature_match = re.search(r'before` parameter set to ([^\.]+)', error_response["error"])
                    if signature_match:
                        last_signature = signature_match.group(1)
                        logging.info(f"Continuing search with signature: {last_signature}")
                        continue
                logging.error(f"Error fetching transactions: {response.text}")
                break
                
            transactions = response.json()
            
            if not transactions or len(transactions) == 0:
                logging.info("No more transactions found")
                break
                
            # Filter transactions by timestamp
            filtered_transactions = [
                tx for tx in transactions 
                if "timestamp" in tx and tx["timestamp"] >= before_timestamp
            ]
            
            # If all transactions are older than 6 months, we're done
            if len(filtered_transactions) == 0 and transactions[0]["timestamp"] < before_timestamp:
                logging.info(datetime.fromtimestamp(transactions[0]["timestamp"]))
                break
                
            all_transactions.extend(filtered_transactions)
            
            # Get the last signature for pagination
            last_signature = transactions[-1]["signature"]
        
        logging.info(f"Found {len(all_transactions)} {transaction_type} transactions")
        
        return all_transactions
    
    @file_cache(ttl_hours=240)
    @lru_cache(maxsize=1024)
    def get_token_decimals(self, mint_address):
        """
        Get token decimals using Helius getAsset endpoint.
        Uses both file caching and LRU caching to reduce API calls.
        
        Args:
            mint_address (str): Token mint address
            
        Returns:
            int: Number of decimals for the token
        """
        url = f"https://mainnet.helius-rpc.com/?api-key={self.api_key}"
        headers = {"Content-Type": "application/json"}
        data = {
            "jsonrpc": "2.0",
            "id": "test",
            "method": "getAsset",
            "params": {
                "id": mint_address
            }
        }
        
        try:
            response = requests.post(url, headers=headers, json=data)
            if response.status_code == 200:
                data = response.json()
                if "result" in data and "token_info" in data["result"] and "decimals" in data["result"]["token_info"]:
                    decimals = data["result"]["token_info"]["decimals"]
                    return decimals
            logging.warning(f"Could not fetch decimals for token {mint_address}")
            return 9  # Default to 9 decimals if not found
        except Exception as e:
            logging.error(f"Error fetching decimals for token {mint_address}: {str(e)}")
            return 9  # Default to 9 decimals on error 