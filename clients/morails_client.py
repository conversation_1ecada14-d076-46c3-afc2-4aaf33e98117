from datetime import datetime, timedelta
import requests
import logging
from functools import lru_cache
from utils.utils import file_cache

# BSC-specific constants
BSC_NATIVE_TOKEN = "******************************************"  # WBNB
BSC_USDT = "******************************************"  # BSC-USD (Tether USD)
BSC_USDC = "******************************************"  # USD Coin
BSC_BUSD = "******************************************"  # Binance USD (deprecated)

# Common BSC DEX router addresses
PANCAKESWAP_V2_ROUTER = "******************************************"
PANCAKESWAP_V3_ROUTER = "******************************************"
PANCAKESWAP_V1_ROUTER = "******************************************"

class MoralisClient:
    """Client for interacting with Moralis API to fetch BSC (Binance Smart Chain) transactions."""
    
    def __init__(self, api_key):
        self.api_key = api_key
        self.base_url = "https://deep-index.moralis.io/api/v2.2"
        self.headers = {
            "X-API-Key": api_key,
            "Content-Type": "application/json"
        }
        
    @file_cache(ttl_hours=240)
    @lru_cache(maxsize=1024)
    def get_swaps(self, wallet_address, months=6):
        """
        Fetch SWAP transactions for a wallet address from the past specified months on BSC.
        Uses both file caching and LRU caching to reduce API calls.

        Args:
            wallet_address (str): BSC wallet address (0x format)
            months (int): Number of months to look back

        Returns:
            list: List of swap transactions
        """
        logging.info(f"Fetching SWAP transactions for {wallet_address} on BSC")

        # Calculate timestamp for months ago
        months_ago = datetime.now() - timedelta(days=30 * months)
        from_date = months_ago.strftime('%Y-%m-%d')

        all_swaps = []
        # Use the dedicated swaps endpoint
        url = f"{self.base_url}/wallets/{wallet_address}/swaps"

        # Parameters for BSC chain
        params = {
            "chain": "bsc",
            "order": "DESC",
            "limit": 100,
            "from_date": from_date
        }

        # Use pagination to get all swaps
        cursor = None
        page = 0
        max_pages = 50  # Safety limit to prevent infinite loops

        while page < max_pages:
            if cursor:
                params["cursor"] = cursor

            try:
                response = requests.get(url, headers=self.headers, params=params)

                if response.status_code != 200:
                    logging.error(f"Error fetching swaps: {response.status_code} - {response.text}")
                    break

                data = response.json()

                if not data or "result" not in data:
                    logging.info("No more swap data found")
                    break

                swaps = data["result"]
                if not swaps:
                    logging.info("No more swaps found")
                    break

                # Filter swaps by date if needed (API might not support from_date)
                filtered_swaps = []
                for swap in swaps:
                    swap_date = swap.get("block_timestamp")
                    if swap_date:
                        # Parse the timestamp and check if it's within our date range
                        try:
                            swap_datetime = datetime.fromisoformat(swap_date.replace('Z', '+00:00'))
                            if swap_datetime >= months_ago:
                                filtered_swaps.append(swap)
                            else:
                                # If we've reached swaps older than our target date, stop
                                logging.info(f"Reached swaps older than {months} months, stopping")
                                return all_swaps
                        except ValueError:
                            # If we can't parse the date, include the swap
                            filtered_swaps.append(swap)
                    else:
                        # If no timestamp, include the swap
                        filtered_swaps.append(swap)

                all_swaps.extend(filtered_swaps)

                # Check for pagination
                cursor = data.get("cursor")
                if not cursor:
                    logging.info("No more pages available")
                    break

                page += 1
                logging.info(f"Processed page {page}, found {len(filtered_swaps)} swaps")

            except Exception as e:
                logging.error(f"Error fetching swaps: {str(e)}")
                break

        logging.info(f"Found {len(all_swaps)} SWAP transactions")
        return all_swaps

    @file_cache(ttl_hours=240)
    @lru_cache(maxsize=1024)
    def get_transactions(self, wallet_address, months=6, transaction_type="SWAP"):
        """
        Fetch transactions for a wallet address from the past specified months on BSC.
        This method provides compatibility with the Helius client interface.
        Uses both file caching and LRU caching to reduce API calls.

        Args:
            wallet_address (str): BSC wallet address (0x format)
            months (int): Number of months to look back
            transaction_type (str): Type of transaction to filter for (currently only supports "SWAP")

        Returns:
            list: List of transactions
        """
        if transaction_type == "SWAP":
            return self.get_swaps(wallet_address, months)
        else:
            logging.warning(f"Transaction type '{transaction_type}' not supported for BSC. Only 'SWAP' is supported.")
            return []

    @file_cache(ttl_hours=240)
    @lru_cache(maxsize=1024)
    def get_token_metadata(self, token_address):
        """
        Get token metadata including decimals for BSC tokens.
        Uses both file caching and LRU caching to reduce API calls.
        
        Args:
            token_address (str): BSC token contract address
            
        Returns:
            dict: Token metadata including decimals, symbol, name
        """
        url = f"{self.base_url}/erc20/metadata"
        params = {
            "chain": "bsc",
            "addresses": [token_address]
        }
        
        try:
            response = requests.post(url, headers=self.headers, json=params)
            if response.status_code == 200:
                data = response.json()
                if data and len(data) > 0:
                    token_data = data[0]
                    return {
                        "decimals": token_data.get("decimals", 18),
                        "symbol": token_data.get("symbol", "UNKNOWN"),
                        "name": token_data.get("name", "Unknown Token"),
                        "address": token_data.get("address", token_address)
                    }
            logging.warning(f"Could not fetch metadata for token {token_address}")
            return {
                "decimals": 18,  # Default to 18 decimals for BSC tokens
                "symbol": "UNKNOWN",
                "name": "Unknown Token",
                "address": token_address
            }
        except Exception as e:
            logging.error(f"Error fetching metadata for token {token_address}: {str(e)}")
            return {
                "decimals": 18,  # Default to 18 decimals on error
                "symbol": "UNKNOWN", 
                "name": "Unknown Token",
                "address": token_address
            }
    
    @file_cache(ttl_hours=240)
    @lru_cache(maxsize=1024)
    def get_token_decimals(self, token_address):
        """
        Get token decimals for BSC tokens.
        Uses both file caching and LRU caching to reduce API calls.
        
        Args:
            token_address (str): BSC token contract address
            
        Returns:
            int: Number of decimals for the token
        """
        metadata = self.get_token_metadata(token_address)
        return metadata["decimals"]
    
    @file_cache(ttl_hours=240)
    @lru_cache(maxsize=1024)
    def get_wallet_token_balances(self, wallet_address):
        """
        Get current token balances for a wallet on BSC.
        Uses both file caching and LRU caching to reduce API calls.
        
        Args:
            wallet_address (str): BSC wallet address
            
        Returns:
            list: List of token balances
        """
        url = f"{self.base_url}/{wallet_address}/erc20"
        params = {
            "chain": "bsc"
        }
        
        try:
            response = requests.get(url, headers=self.headers, params=params)
            if response.status_code == 200:
                return response.json()
            logging.warning(f"Could not fetch token balances for wallet {wallet_address}")
            return []
        except Exception as e:
            logging.error(f"Error fetching token balances for wallet {wallet_address}: {str(e)}")
            return []

    @file_cache(ttl_hours=1)  # Cache for 1 hour since prices change frequently
    @lru_cache(maxsize=100)
    def get_bnb_price(self):
        """
        Get current BNB price in USD using Moralis API.
        Uses both file caching and LRU caching to reduce API calls.

        Returns:
            float: BNB price in USD
        """
        url = f"{self.base_url}/erc20/{BSC_NATIVE_TOKEN}/price"
        params = {
            "chain": "bsc"
        }

        try:
            response = requests.get(url, headers=self.headers, params=params)
            if response.status_code == 200:
                data = response.json()
                price = data.get("usdPrice", 0)
                logging.info(f"BNB price: ${price}")
                return float(price)
            logging.warning("Could not fetch BNB price")
            return 0.0
        except Exception as e:
            logging.error(f"Error fetching BNB price: {str(e)}")
            return 0.0

    @file_cache(ttl_hours=1)  # Cache for 1 hour since prices change frequently
    @lru_cache(maxsize=1000)
    def get_token_price(self, token_address):
        """
        Get current token price in USD using Moralis API.
        Uses both file caching and LRU caching to reduce API calls.

        Args:
            token_address (str): BSC token contract address

        Returns:
            float: Token price in USD
        """
        url = f"{self.base_url}/erc20/{token_address}/price"
        params = {
            "chain": "bsc"
        }

        try:
            response = requests.get(url, headers=self.headers, params=params)
            if response.status_code == 200:
                data = response.json()
                price = data.get("usdPrice", 0)
                return float(price)
            logging.warning(f"Could not fetch price for token {token_address}")
            return 0.0
        except Exception as e:
            logging.error(f"Error fetching price for token {token_address}: {str(e)}")
            return 0.0
