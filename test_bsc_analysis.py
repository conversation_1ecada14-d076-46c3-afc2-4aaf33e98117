#!/usr/bin/env python3
"""
Test script for the BSC analysis pipeline.
"""

import os
import logging
import argparse
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)

def test_bsc_imports():
    """Test that all BSC modules can be imported successfully."""
    try:
        from clients.morails_client import MoralisClient
        print("✓ MoralisClient imported successfully")
        
        from token_constants_bsc import EXCLUDED_TOKENS, BNB_TOKENS, STABLE_TOKENS
        print("✓ BSC token constants imported successfully")
        print(f"  - Excluded tokens: {len(EXCLUDED_TOKENS)}")
        print(f"  - BNB tokens: {BNB_TOKENS}")
        print(f"  - Stable tokens: {len(STABLE_TOKENS)}")
        
        from processing_bsc import munge_transactions_bsc, munge_swaps_bsc
        print("✓ BSC processing functions imported successfully")
        
        from analysis_bsc import analyze_tokens_from_swaps_bsc, calculate_token_metrics_bsc
        print("✓ BSC analysis functions imported successfully")
        
        import main_bsc
        print("✓ BSC main module imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False

def test_bsc_client():
    """Test basic MoralisClient functionality."""
    load_dotenv()
    
    moralis_api_key = os.getenv("MORALIS_API_KEY")
    if not moralis_api_key:
        print("⚠ MORALIS_API_KEY not found in .env file")
        return False
    
    try:
        from clients.morails_client import MoralisClient
        client = MoralisClient(moralis_api_key)
        print("✓ MoralisClient initialized successfully")
        
        # Test BNB price
        bnb_price = client.get_bnb_price()
        print(f"✓ BNB price: ${bnb_price}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing MoralisClient: {e}")
        return False

def test_wallet_address_format():
    """Test wallet address format validation."""
    load_dotenv()
    
    wallet_address = os.getenv("WALLET_ADDRESS")
    if not wallet_address:
        print("⚠ WALLET_ADDRESS not found in .env file")
        return False
    
    print(f"Wallet address: {wallet_address}")
    
    if wallet_address.startswith("0x") and len(wallet_address) == 42:
        print("✓ Wallet address format is correct for BSC")
        return True
    else:
        print("✗ Wallet address format is incorrect for BSC")
        print("  BSC addresses must start with '0x' and be 42 characters long")
        return False

def main():
    """Run all BSC tests."""
    parser = argparse.ArgumentParser(description="Test BSC analysis pipeline")
    parser.add_argument("--full", action="store_true", help="Run full analysis test (requires API keys)")
    args = parser.parse_args()
    
    print("🔍 Testing BSC Analysis Pipeline\n")
    
    # Test 1: Imports
    print("1. Testing imports...")
    if not test_bsc_imports():
        print("❌ Import test failed")
        return
    print()
    
    # Test 2: Wallet address format
    print("2. Testing wallet address format...")
    if not test_wallet_address_format():
        print("❌ Wallet address test failed")
        return
    print()
    
    # Test 3: Client functionality (optional)
    if args.full:
        print("3. Testing MoralisClient...")
        if not test_bsc_client():
            print("❌ Client test failed")
            return
        print()
    
    print("✅ All BSC tests passed!")
    
    if not args.full:
        print("\nTo run full tests including API calls, use:")
        print("python test_bsc_analysis.py --full")

if __name__ == "__main__":
    main()
